#!/usr/bin/env python3

import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, urlunparse
import time
import logging
from typing import Set, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailScraper:
    def __init__(self, delay=1, max_pages=50):
        """
        Initialize the email scraper.

        Args:
            delay (int): Delay between requests in seconds
            max_pages (int): Maximum number of pages to scrape
        """
        self.delay = delay
        self.max_pages = max_pages
        self.visited_urls = set()
        self.found_emails = set()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Email regex pattern
        self.email_pattern = re.compile(
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        )

    def is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and accessible."""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

    def normalize_url(self, url: str) -> str:
        """Normalize URL by removing fragments and query parameters for deduplication."""
        parsed = urlparse(url)
        return urlunparse((parsed.scheme, parsed.netloc, parsed.path, '', '', ''))

    def get_page_content(self, url: str) -> str:
        """Fetch page content with error handling."""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logger.warning(f"Failed to fetch {url}: {e}")
            return ""

    def extract_emails_from_text(self, text: str) -> Set[str]:
        """Extract email addresses from text using regex."""
        emails = set(self.email_pattern.findall(text))
        # Filter out common false positives
        filtered_emails = set()
        for email in emails:
            email_lower = email.lower()
            # Skip emails with common file extensions or suspicious patterns
            if not any(ext in email_lower for ext in ['.jpg', '.png', '.gif', '.css', '.js', '.pdf', '.doc', '.zip']):
                # Additional validation: check for reasonable email format
                if '@' in email and '.' in email.split('@')[1]:
                    # Skip obviously fake emails
                    if not any(fake in email_lower for fake in ['example.com', 'test.com', 'dummy', 'fake']):
                        filtered_emails.add(email_lower)
        return filtered_emails

    def extract_links(self, html: str, base_url: str) -> Set[str]:
        """Extract all links from HTML content."""
        soup = BeautifulSoup(html, 'html.parser')
        links = set()

        for link in soup.find_all('a', href=True):
            href = link['href']
            full_url = urljoin(base_url, href)

            # Only include links from the same domain
            if urlparse(full_url).netloc == urlparse(base_url).netloc:
                normalized_url = self.normalize_url(full_url)
                # Skip common non-content URLs
                if not any(skip in normalized_url.lower() for skip in [
                    '/login', '/logout', '/admin', '/api/', '/download',
                    '.pdf', '.doc', '.zip', '.exe', '.dmg'
                ]):
                    links.add(normalized_url)

        return links

    def scrape_website(self, start_url: str) -> List[str]:
        """
        Scrape a website for email addresses.

        Args:
            start_url (str): The starting URL to scrape

        Returns:
            List[str]: List of unique email addresses found
        """
        if not self.is_valid_url(start_url):
            raise ValueError(f"Invalid URL: {start_url}")

        # Reset state
        self.visited_urls.clear()
        self.found_emails.clear()

        # Queue of URLs to visit
        urls_to_visit = {self.normalize_url(start_url)}

        logger.info(f"Starting to scrape: {start_url}")

        while urls_to_visit and len(self.visited_urls) < self.max_pages:
            current_url = urls_to_visit.pop()

            if current_url in self.visited_urls:
                continue

            logger.info(f"Scraping page {len(self.visited_urls) + 1}: {current_url}")
            self.visited_urls.add(current_url)

            # Get page content
            html_content = self.get_page_content(current_url)
            if not html_content:
                continue

            # Extract emails from the page
            page_emails = self.extract_emails_from_text(html_content)
            self.found_emails.update(page_emails)

            # Extract links for further crawling
            page_links = self.extract_links(html_content, current_url)

            # Add new links to the queue
            for link in page_links:
                if link not in self.visited_urls:
                    urls_to_visit.add(link)

            # Be respectful - add delay between requests
            time.sleep(self.delay)

        logger.info(f"Scraping completed. Found {len(self.found_emails)} unique emails from {len(self.visited_urls)} pages.")
        return sorted(list(self.found_emails))
