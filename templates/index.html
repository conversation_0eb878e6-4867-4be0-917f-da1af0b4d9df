{% extends "base.html" %}

{% block title %}Email Scraper Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-envelope-at"></i> Email Scraper Dashboard
        </h1>
    </div>
</div>

<!-- CSV Upload Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-upload"></i> Upload Company Data
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_csv') }}" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="csvFile" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="csvFile" name="file" accept=".csv" required>
                        <div class="form-text">
                            CSV must contain "name" and "url" columns<br>
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i> URLs will be automatically cleaned to root domain
                                (e.g., "https://example.com/page" → "https://example.com")
                            </small>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-upload"></i> Upload CSV
                        </button>
                        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#sampleCsvModal">
                            <i class="bi bi-question-circle"></i> Sample Format
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Queue Controls -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-play-circle"></i> Scraping Controls
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="maxPages" class="form-label">Max Pages per Site</label>
                    <input type="number" class="form-control" id="maxPages" value="50" min="1" max="100">
                </div>
                <div class="mb-3">
                    <label for="delay" class="form-label">Delay (seconds)</label>
                    <input type="number" class="form-control" id="delay" value="1" min="0.5" max="10" step="0.5">
                </div>
                <button type="button" class="btn btn-success" id="queueAllBtn">
                    <i class="bi bi-play-circle"></i> Queue All Companies
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Status Dashboard -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i> Queue Status
                </h5>
                <button type="button" class="btn btn-outline-primary btn-sm" id="refreshStatusBtn">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
            </div>
            <div class="card-body">
                <div class="row" id="statusCards">
                    <!-- Status cards will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Jobs Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-task"></i> Recent Jobs
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="jobsTable">
                        <thead>
                            <tr>
                                <th>Company</th>
                                <th>URL</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Pages</th>
                                <th>Emails</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="jobsTableBody">
                            <!-- Jobs will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sample CSV Modal -->
<div class="modal fade" id="sampleCsvModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sample CSV Format</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Your CSV file should have the following format:</p>
                <pre>name,url
"Company A","https://example.com/about"
"Company B","www.another-example.com/contact"
"Company C","https://subdomain.example.com/page?query=1"</pre>
                <p class="mt-3">
                    <strong>Required columns:</strong>
                    <br>• <code>name</code> - Company name
                    <br>• <code>url</code> - Company website URL (any format)
                </p>
                <div class="alert alert-info mt-3">
                    <i class="bi bi-info-circle"></i> <strong>URL Cleaning:</strong>
                    URLs are automatically cleaned to root domains:
                    <ul class="mb-0 mt-2">
                        <li><code>https://example.com/about</code> → <code>https://example.com</code></li>
                        <li><code>www.example.com/contact</code> → <code>https://www.example.com</code></li>
                        <li><code>https://sub.example.com/page?q=1</code> → <code>https://sub.example.com</code></li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh status every 10 seconds
setInterval(refreshStatus, 10000);

// Load initial status
$(document).ready(function() {
    refreshStatus();
});
</script>
{% endblock %}
