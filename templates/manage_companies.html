{% extends "base.html" %}

{% block title %}Manage Companies - Email Scraper{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-building"></i> Manage Companies
        </h1>
    </div>
</div>

<!-- Controls Section -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="searchInput" class="form-label">Search Companies</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="Search by name or URL...">
                    </div>
                    <div class="col-md-3">
                        <label for="sortSelect" class="form-label">Sort By</label>
                        <select class="form-select" id="sortSelect">
                            <option value="name">Name (A-Z)</option>
                            <option value="name_desc">Name (Z-A)</option>
                            <option value="url">URL (A-Z)</option>
                            <option value="url_desc">URL (Z-A)</option>
                            <option value="created_at">Newest First</option>
                            <option value="created_at_desc">Oldest First</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary me-2" id="clearFiltersBtn">
                            <i class="bi bi-x-circle"></i> Clear
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCompanyModal">
                            <i class="bi bi-plus-circle"></i> Add Company
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Quick Stats</h6>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary mb-0" id="totalCompanies">-</div>
                        <small class="text-muted">Total Companies</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success mb-0" id="filteredCompanies">-</div>
                        <small class="text-muted">Filtered Results</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Companies Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> Companies List
                </h5>
                <button type="button" class="btn btn-outline-primary btn-sm" id="refreshBtn">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="companiesTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>URL</th>
                                <th>Created</th>
                                <th>Jobs</th>
                                <th>Total Emails</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="companiesTableBody">
                            <!-- Companies will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
                <div id="noResults" class="text-center py-4" style="display: none;">
                    <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-2">No companies found matching your criteria.</p>
                </div>
                <div id="loadingSpinner" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Company Modal -->
<div class="modal fade" id="addCompanyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Company</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCompanyForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="companyName" class="form-label">Company Name *</label>
                        <input type="text" class="form-control" id="companyName" required>
                    </div>
                    <div class="mb-3">
                        <label for="companyUrl" class="form-label">Company URL *</label>
                        <input type="url" class="form-control" id="companyUrl" placeholder="https://example.com" required>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i> URL will be automatically cleaned to root domain
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Company
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Company Modal -->
<div class="modal fade" id="editCompanyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Company</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCompanyForm">
                <div class="modal-body">
                    <input type="hidden" id="editCompanyId">
                    <div class="mb-3">
                        <label for="editCompanyName" class="form-label">Company Name *</label>
                        <input type="text" class="form-control" id="editCompanyName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editCompanyUrl" class="form-label">Company URL *</label>
                        <input type="url" class="form-control" id="editCompanyUrl" required>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i> URL will be automatically cleaned to root domain
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteCompanyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this company?</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Warning:</strong> This will also delete all associated scraping jobs and emails.
                </div>
                <div id="deleteCompanyInfo">
                    <!-- Company info will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="bi bi-trash"></i> Delete Company
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Company Emails Modal -->
<div class="modal fade" id="companyEmailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Company Emails</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="companyEmailsContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="exportCompanyEmailsBtn">
                    <i class="bi bi-download"></i> Export CSV
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/manage_companies.js') }}"></script>
{% endblock %}
