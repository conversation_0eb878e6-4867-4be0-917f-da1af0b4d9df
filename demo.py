#!/usr/bin/env python3
"""
Comprehensive demo script for the Email Scraper API.
This script demonstrates various usage scenarios and features.
"""

import requests
import json
import time
import sys

class EmailScraperDemo:
    def __init__(self, api_url="http://localhost:5001"):
        self.api_url = api_url
        self.scrape_endpoint = f"{api_url}/scrape"
        self.health_endpoint = f"{api_url}/health"
    
    def check_api_health(self):
        """Check if the API is running and healthy."""
        try:
            response = requests.get(self.health_endpoint, timeout=5)
            response.raise_for_status()
            result = response.json()
            print(f"✅ API Health Check: {result['status']}")
            return True
        except requests.exceptions.ConnectionError:
            print("❌ API is not running. Please start the Flask app with: python app.py")
            return False
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return False
    
    def scrape_website(self, url, max_pages=10, delay=1, description=""):
        """Scrape a website and display results."""
        print(f"\n{'='*60}")
        print(f"🔍 Scraping: {url}")
        if description:
            print(f"📝 Description: {description}")
        print(f"⚙️  Settings: max_pages={max_pages}, delay={delay}s")
        print(f"{'='*60}")
        
        payload = {
            "url": url,
            "max_pages": max_pages,
            "delay": delay
        }
        
        try:
            start_time = time.time()
            response = requests.post(self.scrape_endpoint, json=payload, timeout=120)
            response.raise_for_status()
            end_time = time.time()
            
            result = response.json()
            
            print(f"⏱️  Scraping completed in {end_time - start_time:.2f} seconds")
            print(f"📊 Results:")
            print(f"   • Pages scraped: {result['pages_scraped']}")
            print(f"   • Emails found: {result['emails_found']}")
            
            if result['emails']:
                print(f"   • Email addresses:")
                for i, email in enumerate(result['emails'], 1):
                    print(f"     {i:2d}. {email}")
            else:
                print(f"   • No email addresses found")
            
            return result
            
        except requests.exceptions.Timeout:
            print("❌ Request timed out. The website might be slow or unresponsive.")
            return None
        except requests.exceptions.HTTPError as e:
            print(f"❌ HTTP Error: {e}")
            if hasattr(e.response, 'text'):
                try:
                    error_detail = json.loads(e.response.text)
                    print(f"   Error details: {error_detail.get('error', 'Unknown error')}")
                except:
                    pass
            return None
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return None
    
    def run_demo(self):
        """Run the complete demo."""
        print("🚀 Email Scraper API Demo")
        print("=" * 60)
        
        # Check API health
        if not self.check_api_health():
            return
        
        # Demo scenarios
        scenarios = [
            {
                "url": "http://localhost:8082",
                "max_pages": 5,
                "delay": 0.5,
                "description": "Local test server with multiple pages and email addresses"
            },
            {
                "url": "https://httpbin.org/html",
                "max_pages": 1,
                "delay": 1,
                "description": "Simple HTML page (likely no emails)"
            }
        ]
        
        results = []
        for scenario in scenarios:
            result = self.scrape_website(**scenario)
            if result:
                results.append(result)
            time.sleep(1)  # Brief pause between demos
        
        # Summary
        print(f"\n{'='*60}")
        print("📈 DEMO SUMMARY")
        print(f"{'='*60}")
        
        total_emails = sum(r['emails_found'] for r in results)
        total_pages = sum(r['pages_scraped'] for r in results)
        
        print(f"Total scenarios tested: {len(results)}")
        print(f"Total pages scraped: {total_pages}")
        print(f"Total emails found: {total_emails}")
        
        if total_emails > 0:
            print(f"\nAll unique emails found:")
            all_emails = set()
            for result in results:
                all_emails.update(result['emails'])
            
            for i, email in enumerate(sorted(all_emails), 1):
                print(f"  {i:2d}. {email}")
        
        print(f"\n✅ Demo completed successfully!")

def main():
    """Main function to run the demo."""
    demo = EmailScraperDemo()
    
    # Check if test server is running
    try:
        test_response = requests.get("http://localhost:8082", timeout=2)
        if test_response.status_code == 200:
            print("ℹ️  Test server detected at http://localhost:8082")
        else:
            print("⚠️  Test server not responding properly")
    except:
        print("⚠️  Test server not running at http://localhost:8082")
        print("   You can start it with: python test_server.py")
    
    demo.run_demo()

if __name__ == "__main__":
    main()
