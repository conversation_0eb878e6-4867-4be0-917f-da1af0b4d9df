#!/bin/bash

# Email Scraper Startup Script
# This script activates the conda environment and starts the Flask application

echo "🚀 Starting Email Scraper Application"
echo "======================================"

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Error: conda is not installed or not in PATH"
    exit 1
fi

# Check if the email-scrape environment exists
if ! conda env list | grep -q "email-scrape"; then
    echo "❌ Error: conda environment 'email-scrape' not found"
    echo "Please create the environment first:"
    echo "conda create -n email-scrape python=3.13"
    echo "conda activate email-scrape"
    echo "pip install -r requirements.txt"
    exit 1
fi

echo "✅ Conda environment 'email-scrape' found"

# Activate the environment and start the application
echo "🔄 Activating conda environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate email-scrape

echo "✅ Environment activated"
echo "🌐 Starting Flask application on http://localhost:5001"
echo "📝 Press Ctrl+C to stop the server"
echo ""

# Start the Flask application
python app.py
