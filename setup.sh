#!/bin/bash

# Email Scraper Setup Script
# This script sets up the conda environment and installs dependencies

echo "🔧 Email Scraper Setup"
echo "======================"

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Error: conda is not installed or not in PATH"
    echo "Please install Miniconda or Anaconda first:"
    echo "https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

echo "✅ Conda found"

# Check if environment already exists
if conda env list | grep -q "email-scrape"; then
    echo "⚠️  Environment 'email-scrape' already exists"
    read -p "Do you want to recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️  Removing existing environment..."
        conda env remove -n email-scrape -y
    else
        echo "✅ Using existing environment"
        conda activate email-scrape
        pip install -r requirements.txt
        echo "✅ Setup completed!"
        exit 0
    fi
fi

echo "🔄 Creating conda environment 'email-scrape' with Python 3.13..."
conda create -n email-scrape python=3.13 -y

if [ $? -ne 0 ]; then
    echo "❌ Failed to create conda environment"
    exit 1
fi

echo "✅ Environment created successfully"

# Activate environment
echo "🔄 Activating environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate email-scrape

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Test imports
echo "🧪 Testing imports..."
python -c "
import flask
import requests
from bs4 import BeautifulSoup
print('✅ All imports successful!')
"

if [ $? -ne 0 ]; then
    echo "❌ Import test failed"
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "To start the application:"
echo "  ./start.sh"
echo ""
echo "Or manually:"
echo "  conda activate email-scrape"
echo "  python app.py"
echo ""
echo "To run the demo:"
echo "  conda activate email-scrape"
echo "  python demo.py"
