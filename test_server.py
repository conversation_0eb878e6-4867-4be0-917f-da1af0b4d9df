#!/usr/bin/env python3
"""
Simple test server that serves HTML pages with email addresses for testing the scraper.
"""

from flask import Flask, render_template_string
import threading
import time

test_app = Flask(__name__)

# HTML templates with email addresses
HOME_PAGE = """
<!DOCTYPE html>
<html>
<head>
    <title>Test Company</title>
</head>
<body>
    <h1>Welcome to Test Company</h1>
    <p>Contact us at: <a href="mailto:<EMAIL>"><EMAIL></a></p>
    <p>For support, email: <EMAIL></p>
    <p>Sales inquiries: <EMAIL></p>

    <h2>Navigation</h2>
    <ul>
        <li><a href="/about">About Us</a></li>
        <li><a href="/contact">Contact</a></li>
        <li><a href="/team">Our Team</a></li>
    </ul>
</body>
</html>
"""

ABOUT_PAGE = """
<!DOCTYPE html>
<html>
<head>
    <title>About - Test Company</title>
</head>
<body>
    <h1>About Test Company</h1>
    <p>Founded in 2020, we are a leading technology company.</p>
    <p>Reach our CEO at: <EMAIL></p>
    <p>Media inquiries: <EMAIL></p>

    <a href="/">Back to Home</a>
</body>
</html>
"""

CONTACT_PAGE = """
<!DOCTYPE html>
<html>
<head>
    <title>Contact - Test Company</title>
</head>
<body>
    <h1>Contact Information</h1>
    <p>General: <EMAIL></p>
    <p>Technical Support: <EMAIL></p>
    <p>Billing: <EMAIL></p>
    <p>HR Department: <EMAIL></p>

    <a href="/">Back to Home</a>
</body>
</html>
"""

TEAM_PAGE = """
<!DOCTYPE html>
<html>
<head>
    <title>Team - Test Company</title>
</head>
<body>
    <h1>Our Team</h1>
    <p>John Doe - CTO: <EMAIL></p>
    <p>Jane Smith - VP Engineering: <EMAIL></p>
    <p>Bob Johnson - Lead Developer: <EMAIL></p>

    <a href="/">Back to Home</a>
</body>
</html>
"""

@test_app.route('/')
def home():
    return HOME_PAGE

@test_app.route('/about')
def about():
    return ABOUT_PAGE

@test_app.route('/contact')
def contact():
    return CONTACT_PAGE

@test_app.route('/team')
def team():
    return TEAM_PAGE

def run_test_server():
    """Run the test server in a separate thread."""
    test_app.run(host='0.0.0.0', port=8082, debug=False)

if __name__ == '__main__':
    print("Starting test server on http://localhost:8082")
    print("This server provides test pages with email addresses for scraping.")
    print("Press Ctrl+C to stop the server.")
    run_test_server()
