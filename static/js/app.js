// Email Scraper Frontend JavaScript

$(document).ready(function() {
    // Initialize event handlers
    initEventHandlers();

    // Load initial data
    refreshStatus();
});

function initEventHandlers() {
    // Queue All Companies button
    $('#queueAllBtn').click(function() {
        queueAllCompanies();
    });

    // Refresh Status button
    $('#refreshStatusBtn').click(function() {
        refreshStatus();
    });

    // CSV file input change handler
    $('#csvFile').change(function() {
        const file = this.files[0];
        if (file && !file.name.toLowerCase().endsWith('.csv')) {
            alert('Please select a CSV file');
            this.value = '';
        }
    });
}

function queueAllCompanies() {
    const maxPages = parseInt($('#maxPages').val()) || 50;
    const delay = parseFloat($('#delay').val()) || 1.0;

    // Validate inputs
    if (maxPages < 1 || maxPages > 100) {
        alert('Max pages must be between 1 and 100');
        return;
    }

    if (delay < 0.5 || delay > 10) {
        alert('Delay must be between 0.5 and 10 seconds');
        return;
    }

    const $btn = $('#queueAllBtn');
    const originalText = $btn.html();

    // Show loading state
    $btn.addClass('loading').prop('disabled', true);
    $btn.html('<span class="spinner-border spinner-border-sm me-2"></span>Queuing...');

    $.ajax({
        url: '/queue-all',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            max_pages: maxPages,
            delay: delay
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                refreshStatus();
            } else {
                showAlert('error', response.error || 'Failed to queue companies');
            }
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to queue companies';
            showAlert('error', error);
        },
        complete: function() {
            // Reset button state
            $btn.removeClass('loading').prop('disabled', false);
            $btn.html(originalText);
        }
    });
}

function refreshStatus() {
    const $btn = $('#refreshStatusBtn');
    const originalHtml = $btn.html();

    // Show loading state
    $btn.html('<span class="spinner-border spinner-border-sm"></span>');

    $.ajax({
        url: '/queue-status',
        method: 'GET',
        success: function(response) {
            updateStatusCards(response.status_counts);
            updateJobsTable(response.recent_jobs);
        },
        error: function() {
            showAlert('error', 'Failed to refresh status');
        },
        complete: function() {
            $btn.html(originalHtml);
        }
    });
}

function updateStatusCards(statusCounts) {
    const statuses = ['pending', 'running', 'completed', 'failed'];
    let cardsHtml = '';

    statuses.forEach(status => {
        const count = statusCounts[status] || 0;
        const icon = getStatusIcon(status);
        const label = status.charAt(0).toUpperCase() + status.slice(1);

        cardsHtml += `
            <div class="col-md-3 col-sm-6">
                <div class="status-card ${status}">
                    <i class="${icon} mb-2" style="font-size: 1.5rem;"></i>
                    <span class="status-number">${count}</span>
                    <span class="status-label">${label}</span>
                </div>
            </div>
        `;
    });

    $('#statusCards').html(cardsHtml);
}

function updateJobsTable(jobs) {
    let tableHtml = '';

    if (jobs.length === 0) {
        tableHtml = `
            <tr>
                <td colspan="7" class="text-center text-muted">
                    <i class="bi bi-inbox"></i> No jobs found
                </td>
            </tr>
        `;
    } else {
        jobs.forEach(job => {
            const statusBadge = getStatusBadge(job.status);
            const createdAt = formatDateTime(job.created_at);
            const companyName = job.company_name || 'Unknown';
            const url = job.url || '';
            const shortUrl = url.length > 30 ? url.substring(0, 30) + '...' : url;

            tableHtml += `
                <tr>
                    <td>${escapeHtml(companyName)}</td>
                    <td>
                        <a href="${escapeHtml(url)}" target="_blank" class="text-decoration-none" title="${escapeHtml(url)}">
                            ${escapeHtml(shortUrl)}
                        </a>
                    </td>
                    <td>${statusBadge}</td>
                    <td>${createdAt}</td>
                    <td>${job.pages_scraped || 0}</td>
                    <td>${job.emails_found || 0}</td>
                    <td>
                        ${getJobActions(job)}
                    </td>
                </tr>
            `;
        });
    }

    $('#jobsTableBody').html(tableHtml);
}

function getStatusIcon(status) {
    const icons = {
        pending: 'bi bi-clock',
        running: 'bi bi-arrow-repeat',
        completed: 'bi bi-check-circle',
        failed: 'bi bi-x-circle'
    };
    return icons[status] || 'bi bi-question-circle';
}

function getStatusBadge(status) {
    const badges = {
        pending: '<span class="badge bg-warning">Pending</span>',
        running: '<span class="badge bg-info">Running</span>',
        completed: '<span class="badge bg-success">Completed</span>',
        failed: '<span class="badge bg-danger">Failed</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function getJobActions(job) {
    let actions = '';

    if (job.status === 'completed' && job.emails_found > 0) {
        actions += `
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-primary" onclick="viewEmails('${job.job_id}')">
                    <i class="bi bi-envelope"></i> View
                </button>
                <a href="/export/${job.job_id}" class="btn btn-sm btn-outline-success" download>
                    <i class="bi bi-download"></i> Export
                </a>
            </div>
        `;
    }

    if (job.status === 'failed') {
        actions += `
            <button class="btn btn-sm btn-outline-danger" onclick="viewError('${job.job_id}', '${escapeHtml(job.error_message || '')}')">
                <i class="bi bi-exclamation-triangle"></i> Error
            </button>
        `;
    }

    return actions || '<span class="text-muted">-</span>';
}

function viewEmails(jobId) {
    // Fetch emails for this job
    $.ajax({
        url: `/job/${jobId}/emails`,
        method: 'GET',
        success: function(response) {
            showEmailsModal(response.job, response.emails);
        },
        error: function() {
            showAlert('error', 'Failed to load emails');
        }
    });
}

function showEmailsModal(job, emails) {
    let emailsHtml = '';

    if (emails.length === 0) {
        emailsHtml = '<p class="text-muted">No emails found for this job.</p>';
    } else {
        emailsHtml = '<div class="table-responsive"><table class="table table-sm">';
        emailsHtml += '<thead><tr><th>Email Address</th><th>Found On Page</th></tr></thead><tbody>';

        emails.forEach(email => {
            const page = email.found_on_page || 'Unknown';
            emailsHtml += `
                <tr>
                    <td><a href="mailto:${escapeHtml(email.email_address)}">${escapeHtml(email.email_address)}</a></td>
                    <td>${escapeHtml(page)}</td>
                </tr>
            `;
        });

        emailsHtml += '</tbody></table></div>';
    }

    const modalHtml = `
        <div class="modal fade" id="emailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-envelope"></i> Emails for ${escapeHtml(job.company_name || 'Unknown Company')}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <strong>URL:</strong> <a href="${escapeHtml(job.url)}" target="_blank">${escapeHtml(job.url)}</a><br>
                            <strong>Emails Found:</strong> ${job.emails_found}<br>
                            <strong>Status:</strong> ${getStatusBadge(job.status)}
                        </div>
                        ${emailsHtml}
                    </div>
                    <div class="modal-footer">
                        <a href="/export/${job.job_id}" class="btn btn-success" download>
                            <i class="bi bi-download"></i> Export CSV
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#emailsModal').remove();

    // Add modal to body and show
    $('body').append(modalHtml);
    $('#emailsModal').modal('show');
}

function viewError(jobId, errorMessage) {
    const modalHtml = `
        <div class="modal fade" id="errorModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-exclamation-triangle text-danger"></i> Job Error
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p><strong>Job ID:</strong> ${escapeHtml(jobId)}</p>
                        <p><strong>Error Message:</strong></p>
                        <div class="alert alert-danger">
                            ${escapeHtml(errorMessage || 'No error message available')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#errorModal').remove();

    // Add modal to body and show
    $('body').append(modalHtml);
    $('#errorModal').modal('show');
}

function formatDateTime(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    return date.toLocaleString();
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Insert at the top of the main container
    $('main .container').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}
