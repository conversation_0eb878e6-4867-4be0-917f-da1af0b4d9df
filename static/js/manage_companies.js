$(document).ready(function() {
    let companies = [];
    let filteredCompanies = [];

    // Load companies on page load
    loadCompanies();

    // Event listeners
    $('#searchInput').on('input', filterCompanies);
    $('#sortSelect').on('change', filterCompanies);
    $('#clearFiltersBtn').on('click', clearFilters);
    $('#refreshBtn').on('click', loadCompanies);

    // Form submissions
    $('#addCompanyForm').on('submit', handleAddCompany);
    $('#editCompanyForm').on('submit', handleEditCompany);
    $('#confirmDeleteBtn').on('click', handleDeleteCompany);

    function loadCompanies() {
        $('#loadingSpinner').show();
        $('#companiesTableBody').empty();
        $('#noResults').hide();

        $.get('/api/companies')
            .done(function(data) {
                companies = data;
                filterCompanies();
                updateStats();
            })
            .fail(function(xhr) {
                console.error('Error loading companies:', xhr.responseJSON);
                showAlert('Error loading companies: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            })
            .always(function() {
                $('#loadingSpinner').hide();
            });
    }

    function filterCompanies() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const sortBy = $('#sortSelect').val();

        // Filter companies
        filteredCompanies = companies.filter(company => {
            const nameMatch = company.name.toLowerCase().includes(searchTerm);
            const urlMatch = company.url.toLowerCase().includes(searchTerm);

            // Special filter for companies with no emails
            if (sortBy === 'no_emails_only') {
                return (nameMatch || urlMatch) && company.total_emails === 0;
            }

            return nameMatch || urlMatch;
        });

        // Sort companies
        filteredCompanies.sort((a, b) => {
            switch(sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'name_desc':
                    return b.name.localeCompare(a.name);
                case 'url':
                    return a.url.localeCompare(b.url);
                case 'url_desc':
                    return b.url.localeCompare(a.url);
                case 'created_at':
                    return new Date(b.created_at) - new Date(a.created_at);
                case 'created_at_desc':
                    return new Date(a.created_at) - new Date(b.created_at);
                case 'emails_found':
                    return a.total_emails - b.total_emails;
                case 'emails_found_desc':
                    return b.total_emails - a.total_emails;
                case 'no_emails_only':
                    return a.name.localeCompare(b.name); // Sort by name when filtering no emails
                default:
                    return 0;
            }
        });

        renderCompaniesTable();
        updateStats();
    }

    function renderCompaniesTable() {
        const tbody = $('#companiesTableBody');
        tbody.empty();

        if (filteredCompanies.length === 0) {
            $('#noResults').show();
            return;
        }

        $('#noResults').hide();

        filteredCompanies.forEach(company => {
            const statusBadge = getCompanyStatusBadge(company);
            const migrateButton = company.total_emails === 0 ?
                `<button type="button" class="btn btn-outline-warning" onclick="migrateCompanyToReview(${company.id})" title="Migrate to Review">
                    <i class="bi bi-arrow-right"></i>
                </button>` : '';

            const row = $(`
                <tr>
                    <td>
                        <strong>${escapeHtml(company.name)}</strong>
                    </td>
                    <td>
                        <a href="${escapeHtml(company.url)}" target="_blank" class="text-decoration-none">
                            ${escapeHtml(company.url)}
                            <i class="bi bi-box-arrow-up-right ms-1"></i>
                        </a>
                    </td>
                    <td>
                        <small class="text-muted">
                            ${formatDate(company.created_at)}
                        </small>
                    </td>
                    <td>
                        <span class="badge bg-secondary">${company.job_count}</span>
                    </td>
                    <td>
                        <span class="badge ${company.total_emails > 0 ? 'bg-success' : 'bg-warning'}">${company.total_emails}</span>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="viewCompanyEmails(${company.id})" title="View Emails">
                                <i class="bi bi-envelope"></i>
                            </button>
                            ${migrateButton}
                            <button type="button" class="btn btn-outline-secondary" onclick="editCompany(${company.id})" title="Edit">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteCompany(${company.id})" title="Delete">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `);
            tbody.append(row);
        });
    }

    function updateStats() {
        $('#totalCompanies').text(companies.length);
        $('#filteredCompanies').text(filteredCompanies.length);
    }

    function clearFilters() {
        $('#searchInput').val('');
        $('#sortSelect').val('name');
        filterCompanies();
    }

    function getCompanyStatusBadge(company) {
        if (company.total_emails > 0) {
            return '<span class="badge bg-success">Has Emails</span>';
        } else if (company.job_count > 0) {
            return '<span class="badge bg-warning">No Emails Found</span>';
        } else {
            return '<span class="badge bg-secondary">Not Processed</span>';
        }
    }

    function handleAddCompany(e) {
        e.preventDefault();

        const name = $('#companyName').val().trim();
        const url = $('#companyUrl').val().trim();

        if (!name || !url) {
            showAlert('Please fill in all required fields', 'warning');
            return;
        }

        $.ajax({
            url: '/api/companies',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                name: name,
                url: url
            })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#addCompanyModal').modal('hide');
            $('#addCompanyForm')[0].reset();
            loadCompanies();
        })
        .fail(function(xhr) {
            showAlert('Error adding company: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    function handleEditCompany(e) {
        e.preventDefault();

        const companyId = $('#editCompanyId').val();
        const name = $('#editCompanyName').val().trim();
        const url = $('#editCompanyUrl').val().trim();

        if (!name || !url) {
            showAlert('Please fill in all required fields', 'warning');
            return;
        }

        $.ajax({
            url: `/api/companies/${companyId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                name: name,
                url: url
            })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#editCompanyModal').modal('hide');
            loadCompanies();
        })
        .fail(function(xhr) {
            showAlert('Error updating company: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    function handleDeleteCompany() {
        const companyId = $('#confirmDeleteBtn').data('company-id');

        $.ajax({
            url: `/api/companies/${companyId}`,
            method: 'DELETE'
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#deleteCompanyModal').modal('hide');
            loadCompanies();
        })
        .fail(function(xhr) {
            showAlert('Error deleting company: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    // Global functions for button clicks
    window.editCompany = function(companyId) {
        const company = companies.find(c => c.id === companyId);
        if (company) {
            $('#editCompanyId').val(company.id);
            $('#editCompanyName').val(company.name);
            $('#editCompanyUrl').val(company.url);
            $('#editCompanyModal').modal('show');
        }
    };

    window.deleteCompany = function(companyId) {
        const company = companies.find(c => c.id === companyId);
        if (company) {
            $('#deleteCompanyInfo').html(`
                <strong>Company:</strong> ${escapeHtml(company.name)}<br>
                <strong>URL:</strong> ${escapeHtml(company.url)}<br>
                <strong>Jobs:</strong> ${company.job_count}<br>
                <strong>Emails:</strong> ${company.total_emails}
            `);
            $('#confirmDeleteBtn').data('company-id', companyId);
            $('#deleteCompanyModal').modal('show');
        }
    };

    window.viewCompanyEmails = function(companyId) {
        const company = companies.find(c => c.id === companyId);
        if (company) {
            $('#companyEmailsContent').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');
            $('#exportCompanyEmailsBtn').data('company-id', companyId);
            $('#companyEmailsModal').modal('show');

            $.get(`/api/companies/${companyId}/emails`)
                .done(function(data) {
                    let content = `<h6>${escapeHtml(data.company.name)} - ${data.emails.length} emails</h6>`;

                    if (data.emails.length === 0) {
                        content += '<p class="text-muted">No emails found for this company.</p>';
                    } else {
                        content += '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Email</th><th>Found On</th><th>Date</th></tr></thead><tbody>';
                        data.emails.forEach(email => {
                            content += `
                                <tr>
                                    <td>${escapeHtml(email.email_address)}</td>
                                    <td>${email.found_on_page ? `<small>${escapeHtml(email.found_on_page)}</small>` : '-'}</td>
                                    <td><small>${formatDate(email.created_at)}</small></td>
                                </tr>
                            `;
                        });
                        content += '</tbody></table></div>';
                    }

                    $('#companyEmailsContent').html(content);
                })
                .fail(function(xhr) {
                    $('#companyEmailsContent').html('<div class="alert alert-danger">Error loading emails</div>');
                });
        }
    };

    // Export company emails
    $('#exportCompanyEmailsBtn').on('click', function() {
        const companyId = $(this).data('company-id');
        if (companyId) {
            // This would need to be implemented as a separate export endpoint
            window.open(`/api/companies/${companyId}/emails/export`, '_blank');
        }
    });

    // Utility functions
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
    }

    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container').prepend(alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').first().alert('close');
        }, 5000);
    }

    // Global function for migration
    window.migrateCompanyToReview = function(companyId) {
        if (confirm('Are you sure you want to migrate this company to the review table?')) {
            $.ajax({
                url: `/api/companies/${companyId}/migrate`,
                method: 'POST'
            })
            .done(function(response) {
                showAlert(response.message, 'success');
                loadCompanies(); // Refresh the list
            })
            .fail(function(xhr) {
                showAlert('Error migrating company: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            });
        }
    };
});
