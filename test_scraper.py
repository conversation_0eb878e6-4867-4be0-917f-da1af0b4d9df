#!/usr/bin/env python3
"""
Test script for the email scraper API.
This script demonstrates how to use the email scraper programmatically.
"""

import requests
import json

def test_email_scraper():
    """Test the email scraper API with a sample website."""
    
    # API endpoint
    url = "http://localhost:5001/scrape"
    
    # Test data
    test_data = {
        "url": "https://httpbin.org/html",
        "max_pages": 1,
        "delay": 1
    }
    
    print("Testing Email Scraper API...")
    print(f"Target URL: {test_data['url']}")
    print(f"Max pages: {test_data['max_pages']}")
    print(f"Delay: {test_data['delay']} seconds")
    print("-" * 50)
    
    try:
        # Make the request
        response = requests.post(url, json=test_data, timeout=30)
        response.raise_for_status()
        
        # Parse the response
        result = response.json()
        
        print("✅ Request successful!")
        print(f"📊 Results:")
        print(f"   - Pages scraped: {result['pages_scraped']}")
        print(f"   - Emails found: {result['emails_found']}")
        
        if result['emails']:
            print(f"   - Email addresses:")
            for email in result['emails']:
                print(f"     • {email}")
        else:
            print(f"   - No email addresses found")
        
        print(f"\n📋 Full response:")
        print(json.dumps(result, indent=2))
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API.")
        print("   Make sure the Flask app is running on http://localhost:5001")
        print("   Run: python app.py")
        
    except requests.exceptions.Timeout:
        print("❌ Error: Request timed out.")
        
    except requests.exceptions.HTTPError as e:
        print(f"❌ HTTP Error: {e}")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_email_scraper()
