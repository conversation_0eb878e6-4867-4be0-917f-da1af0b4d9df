#!/usr/bin/env python3
"""
Test script for URL cleaning functionality.
"""

import sys
import os

# Add the current directory to Python path so we can import from app.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import clean_url_to_root_domain

def test_url_cleaning():
    """Test the URL cleaning function with various inputs."""
    
    test_cases = [
        # (input_url, expected_output)
        ("https://www.example.com/about/company", "https://www.example.com"),
        ("http://example.com/contact?form=1", "http://example.com"),
        ("https://subdomain.example.com/page/deep/path", "https://subdomain.example.com"),
        ("www.google.com/search?q=test", "https://www.google.com"),
        ("example.com/path", "https://example.com"),
        ("https://github.com/user/repo", "https://github.com"),
        ("http://localhost:8082/test", "http://localhost:8082"),
        ("https://www.python.org/downloads/", "https://www.python.org"),
        ("httpbin.org/html?test=1", "https://httpbin.org"),
        ("https://api.example.com/v1/users", "https://api.example.com"),
    ]
    
    print("🧪 Testing URL Cleaning Function")
    print("=" * 60)
    
    all_passed = True
    
    for i, (input_url, expected) in enumerate(test_cases, 1):
        result = clean_url_to_root_domain(input_url)
        passed = result == expected
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{i:2d}. {status}")
        print(f"    Input:    {input_url}")
        print(f"    Expected: {expected}")
        print(f"    Got:      {result}")
        
        if not passed:
            all_passed = False
        
        print()
    
    print("=" * 60)
    if all_passed:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return all_passed

if __name__ == "__main__":
    test_url_cleaning()
