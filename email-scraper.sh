#!/bin/bash

# Email Scraper Management Script
# Usage: ./email-scraper.sh [start|stop|restart|status]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONDA_ENV_NAME="email-scrape"
APP_FILE="app.py"
HOST="0.0.0.0"
PORT="5001"

# Function to show usage
show_usage() {
    echo -e "${BLUE}📋 Email Scraper Management Script${NC}"
    echo "Usage: $0 [start|stop|restart|status]"
    echo ""
    echo "Commands:"
    echo "  start   - Start the email scraper server"
    echo "  stop    - Stop the email scraper server"
    echo "  restart - Stop and then start the server"
    echo "  status  - Check if the server is running"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 stop"
    echo "  $0 restart"
    echo "  $0 status"
}

# Function to check if server is running
check_status() {
    if lsof -Pi :${PORT} -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Email Scraper is running on port ${PORT}${NC}"
        echo "Processes:"
        lsof -Pi :${PORT} -sTCP:LISTEN
        echo ""
        echo -e "${BLUE}📍 Access URLs:${NC}"
        echo -e "  Dashboard: http://localhost:${PORT}"
        echo -e "  Companies: http://localhost:${PORT}/manage-companies"
        echo -e "  Emails:    http://localhost:${PORT}/manage-emails"
        echo -e "  API:       http://localhost:${PORT}/api"
        return 0
    else
        echo -e "${RED}❌ Email Scraper is not running${NC}"
        return 1
    fi
}

# Function to stop the server
stop_server() {
    echo -e "${YELLOW}🛑 Stopping Email Scraper...${NC}"
    
    if lsof -Pi :${PORT} -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}🔪 Killing processes on port ${PORT}...${NC}"
        lsof -ti:${PORT} | xargs kill -9 2>/dev/null || true
        
        # Wait and verify
        sleep 2
        if lsof -Pi :${PORT} -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo -e "${RED}❌ Some processes may still be running${NC}"
            return 1
        else
            echo -e "${GREEN}✅ Email Scraper stopped successfully${NC}"
            return 0
        fi
    else
        echo -e "${GREEN}✅ Email Scraper was not running${NC}"
        return 0
    fi
}

# Function to start the server
start_server() {
    echo -e "${BLUE}🚀 Starting Email Scraper${NC}"
    echo "=================================="
    
    # Check if already running
    if lsof -Pi :${PORT} -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Server is already running on port ${PORT}${NC}"
        check_status
        return 0
    fi
    
    # Check if conda is available
    if ! command -v conda &> /dev/null; then
        echo -e "${RED}❌ Error: conda is not installed or not in PATH${NC}"
        echo "Please install Anaconda/Miniconda or add conda to your PATH"
        return 1
    fi
    
    # Check if the conda environment exists
    if ! conda env list | grep -q "^${CONDA_ENV_NAME} "; then
        echo -e "${RED}❌ Error: Conda environment '${CONDA_ENV_NAME}' not found${NC}"
        echo "Available environments:"
        conda env list
        echo ""
        echo "To create the environment, run:"
        echo "conda create -n ${CONDA_ENV_NAME} python=3.13"
        return 1
    fi
    
    # Check if app.py exists
    if [ ! -f "${APP_FILE}" ]; then
        echo -e "${RED}❌ Error: ${APP_FILE} not found in current directory${NC}"
        echo "Current directory: $(pwd)"
        return 1
    fi
    
    echo -e "${YELLOW}📁 Working directory: $(pwd)${NC}"
    echo -e "${YELLOW}🐍 Activating conda environment: ${CONDA_ENV_NAME}${NC}"
    
    # Initialize conda for bash
    eval "$(conda shell.bash hook)"
    
    # Activate the conda environment
    if conda activate "${CONDA_ENV_NAME}"; then
        echo -e "${GREEN}✅ Successfully activated conda environment: ${CONDA_ENV_NAME}${NC}"
    else
        echo -e "${RED}❌ Failed to activate conda environment: ${CONDA_ENV_NAME}${NC}"
        return 1
    fi
    
    # Show Python info
    echo -e "${YELLOW}🐍 Python version: $(python --version)${NC}"
    
    # Check required packages
    echo -e "${YELLOW}📦 Checking required packages...${NC}"
    REQUIRED_PACKAGES=("flask" "requests" "beautifulsoup4" "lxml")
    MISSING_PACKAGES=()
    
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if ! python -c "import ${package}" 2>/dev/null; then
            MISSING_PACKAGES+=("${package}")
        fi
    done
    
    if [ ${#MISSING_PACKAGES[@]} -ne 0 ]; then
        echo -e "${RED}❌ Missing required packages: ${MISSING_PACKAGES[*]}${NC}"
        echo -e "${YELLOW}💡 Installing missing packages...${NC}"
        
        for package in "${MISSING_PACKAGES[@]}"; do
            echo -e "${YELLOW}Installing ${package}...${NC}"
            if ! pip install "${package}"; then
                echo -e "${RED}❌ Failed to install ${package}${NC}"
                return 1
            fi
        done
        echo -e "${GREEN}✅ All packages installed successfully${NC}"
    else
        echo -e "${GREEN}✅ All required packages are installed${NC}"
    fi
    
    # Create database if it doesn't exist
    if [ ! -f "email_scraper.db" ]; then
        echo -e "${YELLOW}🗄️  Database will be created on first run${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}🎯 Starting Email Scraper...${NC}"
    echo -e "${BLUE}📍 URLs will be available at:${NC}"
    echo -e "  Dashboard: http://localhost:${PORT}"
    echo -e "  Companies: http://localhost:${PORT}/manage-companies"
    echo -e "  Emails:    http://localhost:${PORT}/manage-emails"
    echo -e "  API:       http://localhost:${PORT}/api"
    echo ""
    echo -e "${YELLOW}💡 Press Ctrl+C to stop the server${NC}"
    echo "=================================="
    
    # Launch the Flask app
    python "${APP_FILE}"
}

# Main script logic
case "${1:-}" in
    "start")
        start_server
        ;;
    "stop")
        stop_server
        ;;
    "restart")
        echo -e "${BLUE}🔄 Restarting Email Scraper...${NC}"
        stop_server
        sleep 2
        start_server
        ;;
    "status")
        check_status
        ;;
    "")
        echo -e "${RED}❌ No command specified${NC}"
        echo ""
        show_usage
        exit 1
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        show_usage
        exit 1
        ;;
esac
