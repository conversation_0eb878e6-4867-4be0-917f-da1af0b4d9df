from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, Response
import logging
import csv
import io
import uuid
import threading
import time
import sqlite3
from urllib.parse import urlparse, urlunparse
from scraper import EmailScraper

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'your-secret-key-for-local-use'  # For flash messages

# Database configuration
DATABASE_PATH = 'email_scraper.db'

def init_database():
    """Initialize the SQLite database with required tables."""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Companies table - stores imported company data
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS companies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            url TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Scrape jobs table - tracks scraping jobs and their status
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS scrape_jobs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            job_id TEXT UNIQUE NOT NULL,
            company_id INTEGER,
            url TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            max_pages INTEGER DEFAULT 50,
            delay REAL DEFAULT 1.0,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            error_message TEXT,
            pages_scraped INTEGER DEFAULT 0,
            emails_found INTEGER DEFAULT 0,
            FOREIGN KEY (company_id) REFERENCES companies (id)
        )
    ''')

    # Emails table - stores individual email addresses found
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS emails (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            job_id TEXT NOT NULL,
            email_address TEXT NOT NULL,
            found_on_page TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (job_id) REFERENCES scrape_jobs (job_id)
        )
    ''')

    # Create indexes for better performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_companies_url ON companies(url)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_scrape_jobs_status ON scrape_jobs(status)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_scrape_jobs_job_id ON scrape_jobs(job_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_emails_job_id ON emails(job_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_emails_address ON emails(email_address)')

    conn.commit()
    conn.close()
    logger.info("Database initialized successfully")

def get_db_connection():
    """Get a database connection."""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # This enables column access by name
    return conn

# Initialize database on startup
init_database()

def clean_url_to_root_domain(url):
    """
    Clean URL to extract root domain only.

    Examples:
    - https://www.example.com/something/page -> https://www.example.com
    - http://example.com/path?query=1 -> http://example.com
    - https://subdomain.example.com/page -> https://subdomain.example.com
    """
    try:
        if not url.startswith(('http://', 'https://')):
            # Add https:// if no protocol specified
            url = 'https://' + url

        parsed = urlparse(url)

        # Reconstruct URL with just scheme and netloc (domain)
        clean_url = urlunparse((
            parsed.scheme,      # http or https
            parsed.netloc,      # domain (including subdomain if present)
            '',                 # path (empty)
            '',                 # params (empty)
            '',                 # query (empty)
            ''                  # fragment (empty)
        ))

        return clean_url

    except Exception as e:
        logger.warning(f"Failed to clean URL '{url}': {e}")
        return url  # Return original URL if cleaning fails

# Background processing
background_processor_running = False

def process_scrape_job(job_id, url, max_pages, delay):
    """Process a single scrape job."""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Update job status to running
        cursor.execute(
            'UPDATE scrape_jobs SET status = "running", started_at = CURRENT_TIMESTAMP WHERE job_id = ?',
            (job_id,)
        )
        conn.commit()

        logger.info(f"Starting scrape job {job_id} for URL: {url}")

        # Create scraper and scrape
        scraper = EmailScraper(delay=delay, max_pages=max_pages)
        emails = scraper.scrape_website(url)

        # Store results
        for email in emails:
            cursor.execute(
                'INSERT INTO emails (job_id, email_address) VALUES (?, ?)',
                (job_id, email)
            )

        # Update job as completed
        cursor.execute('''
            UPDATE scrape_jobs
            SET status = "completed", completed_at = CURRENT_TIMESTAMP,
                pages_scraped = ?, emails_found = ?
            WHERE job_id = ?
        ''', (len(scraper.visited_urls), len(emails), job_id))

        conn.commit()
        logger.info(f"Completed scrape job {job_id}. Found {len(emails)} emails from {len(scraper.visited_urls)} pages.")

    except Exception as e:
        logger.error(f"Error processing job {job_id}: {e}")
        cursor.execute(
            'UPDATE scrape_jobs SET status = "failed", error_message = ?, completed_at = CURRENT_TIMESTAMP WHERE job_id = ?',
            (str(e), job_id)
        )
        conn.commit()

    finally:
        conn.close()

def background_processor():
    """Background thread to process scraping jobs."""
    global background_processor_running

    while background_processor_running:
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Get next pending job
            cursor.execute(
                'SELECT job_id, url, max_pages, delay FROM scrape_jobs WHERE status = "pending" ORDER BY created_at LIMIT 1'
            )
            job = cursor.fetchone()

            if job:
                process_scrape_job(job['job_id'], job['url'], job['max_pages'], job['delay'])
            else:
                # No jobs pending, sleep for a bit
                time.sleep(5)

            conn.close()

        except Exception as e:
            logger.error(f"Error in background processor: {e}")
            time.sleep(10)  # Wait longer on error

def start_background_processor():
    """Start the background processor if not already running."""
    global background_processor_running

    if not background_processor_running:
        background_processor_running = True
        processor_thread = threading.Thread(target=background_processor, daemon=True)
        processor_thread.start()
        logger.info("Background processor started")

@app.route('/')
def home():
    """Main UI page."""
    return render_template('index.html')

@app.route('/api')
def api_info():
    """API endpoint with usage instructions."""
    return jsonify({
        "message": "Email Scraper API",
        "usage": "POST to /scrape with JSON body containing 'url' field",
        "example": {
            "url": "https://example.com"
        },
        "optional_parameters": {
            "max_pages": "Maximum number of pages to scrape (default: 50)",
            "delay": "Delay between requests in seconds (default: 1)"
        }
    })

@app.route('/scrape', methods=['POST'])
def scrape_emails():
    """
    Scrape emails from a website.

    Expected JSON payload:
    {
        "url": "https://example.com",
        "max_pages": 50,  # optional
        "delay": 1        # optional
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        url = data.get('url')
        if not url:
            return jsonify({"error": "URL is required"}), 400

        # Clean URL to root domain
        clean_url = clean_url_to_root_domain(url)

        # Log URL cleaning if it changed
        if clean_url != url:
            logger.info(f"Cleaned URL: '{url}' -> '{clean_url}'")

        # Optional parameters
        max_pages = data.get('max_pages', 50)
        delay = data.get('delay', 1)

        # Validate parameters
        if not isinstance(max_pages, int) or max_pages <= 0:
            return jsonify({"error": "max_pages must be a positive integer"}), 400

        if not isinstance(delay, (int, float)) or delay < 0:
            return jsonify({"error": "delay must be a non-negative number"}), 400

        # Limit max_pages to prevent abuse
        max_pages = min(max_pages, 100)

        logger.info(f"Starting email scrape for URL: {clean_url}")

        # Create scraper instance and scrape
        scraper = EmailScraper(delay=delay, max_pages=max_pages)
        emails = scraper.scrape_website(clean_url)

        response = {
            "original_url": url,
            "cleaned_url": clean_url,
            "url": clean_url,  # For backward compatibility
            "emails_found": len(emails),
            "emails": emails,
            "pages_scraped": len(scraper.visited_urls),
            "parameters": {
                "max_pages": max_pages,
                "delay": delay
            }
        }

        logger.info(f"Scraping completed. Found {len(emails)} emails from {len(scraper.visited_urls)} pages.")
        return jsonify(response)

    except ValueError as e:
        logger.error(f"Validation error: {e}")
        return jsonify({"error": str(e)}), 400

    except Exception as e:
        logger.error(f"Unexpected error during scraping: {e}")
        return jsonify({"error": "An unexpected error occurred during scraping"}), 500

@app.route('/upload-csv', methods=['POST'])
def upload_csv():
    """Upload CSV file with company data."""
    try:
        if 'file' not in request.files:
            flash('No file selected', 'error')
            return redirect(url_for('home'))

        file = request.files['file']
        if file.filename == '':
            flash('No file selected', 'error')
            return redirect(url_for('home'))

        if not file.filename.lower().endswith('.csv'):
            flash('Please upload a CSV file', 'error')
            return redirect(url_for('home'))

        # Read CSV content
        stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        csv_reader = csv.DictReader(stream)

        # Validate CSV headers
        required_headers = ['name', 'url']
        if not all(header.lower() in [h.lower() for h in csv_reader.fieldnames] for header in required_headers):
            flash('CSV must contain "name" and "url" columns', 'error')
            return redirect(url_for('home'))

        # Insert companies into database
        conn = get_db_connection()
        cursor = conn.cursor()

        companies_added = 0
        companies_skipped = 0

        for row in csv_reader:
            name = row.get('name', '').strip()
            url = row.get('url', '').strip()

            if not name or not url:
                companies_skipped += 1
                continue

            # Clean URL to root domain
            clean_url = clean_url_to_root_domain(url)

            # Log URL cleaning if it changed
            if clean_url != url:
                logger.info(f"Cleaned URL: '{url}' -> '{clean_url}'")

            # Check if company already exists (using cleaned URL)
            cursor.execute('SELECT id FROM companies WHERE url = ?', (clean_url,))
            if cursor.fetchone():
                companies_skipped += 1
                continue

            # Insert new company with cleaned URL
            cursor.execute(
                'INSERT INTO companies (name, url) VALUES (?, ?)',
                (name, clean_url)
            )
            companies_added += 1

        conn.commit()
        conn.close()

        flash(f'Successfully imported {companies_added} companies. {companies_skipped} skipped (duplicates or invalid data).', 'success')
        return redirect(url_for('home'))

    except Exception as e:
        logger.error(f"Error uploading CSV: {e}")
        flash('Error processing CSV file', 'error')
        return redirect(url_for('home'))

@app.route('/queue-all', methods=['POST'])
def queue_all_companies():
    """Queue all companies for scraping."""
    try:
        data = request.get_json() or {}
        max_pages = data.get('max_pages', 50)
        delay = data.get('delay', 1.0)

        conn = get_db_connection()
        cursor = conn.cursor()

        # Get all companies
        cursor.execute('SELECT id, name, url FROM companies')
        companies = cursor.fetchall()

        jobs_created = 0
        for company in companies:
            job_id = str(uuid.uuid4())

            # Check if there's already a pending or running job for this company
            cursor.execute(
                'SELECT id FROM scrape_jobs WHERE company_id = ? AND status IN ("pending", "running")',
                (company['id'],)
            )
            if cursor.fetchone():
                continue  # Skip if already queued

            # Create new job
            cursor.execute('''
                INSERT INTO scrape_jobs (job_id, company_id, url, max_pages, delay)
                VALUES (?, ?, ?, ?, ?)
            ''', (job_id, company['id'], company['url'], max_pages, delay))
            jobs_created += 1

        conn.commit()
        conn.close()

        # Start background processing if not already running
        if jobs_created > 0:
            start_background_processor()

        return jsonify({
            'success': True,
            'jobs_created': jobs_created,
            'message': f'Queued {jobs_created} companies for scraping'
        })

    except Exception as e:
        logger.error(f"Error queuing companies: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/queue-status')
def queue_status():
    """Get current queue status."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get job counts by status
        cursor.execute('''
            SELECT status, COUNT(*) as count
            FROM scrape_jobs
            GROUP BY status
        ''')
        status_counts = {row['status']: row['count'] for row in cursor.fetchall()}

        # Get recent jobs
        cursor.execute('''
            SELECT sj.job_id, sj.status, sj.created_at, sj.started_at, sj.completed_at,
                   sj.pages_scraped, sj.emails_found, sj.error_message,
                   c.name as company_name, c.url
            FROM scrape_jobs sj
            LEFT JOIN companies c ON sj.company_id = c.id
            ORDER BY sj.created_at DESC
            LIMIT 20
        ''')
        recent_jobs = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'status_counts': status_counts,
            'recent_jobs': recent_jobs
        })

    except Exception as e:
        logger.error(f"Error getting queue status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/companies')
def get_companies():
    """Get all companies."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT id, name, url, created_at FROM companies ORDER BY created_at DESC')
        companies = [dict(row) for row in cursor.fetchall()]

        conn.close()
        return jsonify(companies)

    except Exception as e:
        logger.error(f"Error getting companies: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/job/<job_id>/emails')
def get_job_emails(job_id):
    """Get emails for a specific job."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get job info
        cursor.execute('''
            SELECT sj.job_id, sj.status, sj.emails_found, c.name as company_name, c.url
            FROM scrape_jobs sj
            LEFT JOIN companies c ON sj.company_id = c.id
            WHERE sj.job_id = ?
        ''', (job_id,))
        job = cursor.fetchone()

        if not job:
            return jsonify({'error': 'Job not found'}), 404

        # Get emails
        cursor.execute('''
            SELECT email_address, found_on_page, created_at
            FROM emails
            WHERE job_id = ?
            ORDER BY email_address
        ''', (job_id,))
        emails = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'job': dict(job),
            'emails': emails
        })

    except Exception as e:
        logger.error(f"Error getting job emails: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/export/<job_id>')
def export_job_emails(job_id):
    """Export emails for a job as CSV."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get job info
        cursor.execute('''
            SELECT sj.job_id, c.name as company_name
            FROM scrape_jobs sj
            LEFT JOIN companies c ON sj.company_id = c.id
            WHERE sj.job_id = ?
        ''', (job_id,))
        job = cursor.fetchone()

        if not job:
            return jsonify({'error': 'Job not found'}), 404

        # Get emails
        cursor.execute('''
            SELECT email_address, found_on_page, created_at
            FROM emails
            WHERE job_id = ?
            ORDER BY email_address
        ''', (job_id,))
        emails = cursor.fetchall()

        conn.close()

        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(['Email', 'Found On Page', 'Date Found'])

        for email in emails:
            writer.writerow([email['email_address'], email['found_on_page'] or '', email['created_at']])

        csv_content = output.getvalue()
        output.close()

        # Return CSV as download
        return Response(
            csv_content,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename=emails_{job["company_name"]}_{job_id[:8]}.csv'}
        )

    except Exception as e:
        logger.error(f"Error exporting job emails: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/manage-companies')
def manage_companies():
    """Companies management page."""
    return render_template('manage_companies.html')

@app.route('/manage-emails')
def manage_emails():
    """Emails management page."""
    return render_template('manage_emails.html')

@app.route('/api/companies', methods=['GET'])
def api_get_companies():
    """Get companies with additional stats."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get companies with job and email counts
        cursor.execute('''
            SELECT
                c.id, c.name, c.url, c.created_at,
                COUNT(DISTINCT sj.id) as job_count,
                COALESCE(SUM(sj.emails_found), 0) as total_emails
            FROM companies c
            LEFT JOIN scrape_jobs sj ON c.id = sj.company_id
            GROUP BY c.id, c.name, c.url, c.created_at
            ORDER BY c.created_at DESC
        ''')
        companies = [dict(row) for row in cursor.fetchall()]

        conn.close()
        return jsonify(companies)

    except Exception as e:
        logger.error(f"Error getting companies with stats: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/companies', methods=['POST'])
def api_add_company():
    """Add a new company."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        name = data.get('name', '').strip()
        url = data.get('url', '').strip()

        if not name or not url:
            return jsonify({'error': 'Name and URL are required'}), 400

        # Clean URL to root domain
        clean_url = clean_url_to_root_domain(url)

        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if company already exists
        cursor.execute('SELECT id FROM companies WHERE url = ?', (clean_url,))
        if cursor.fetchone():
            conn.close()
            return jsonify({'error': 'Company with this URL already exists'}), 400

        # Insert new company
        cursor.execute(
            'INSERT INTO companies (name, url) VALUES (?, ?)',
            (name, clean_url)
        )
        company_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logger.info(f"Added new company: {name} - {clean_url}")
        return jsonify({
            'success': True,
            'company_id': company_id,
            'message': 'Company added successfully',
            'cleaned_url': clean_url
        })

    except Exception as e:
        logger.error(f"Error adding company: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/companies/<int:company_id>', methods=['PUT'])
def api_update_company(company_id):
    """Update a company."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        name = data.get('name', '').strip()
        url = data.get('url', '').strip()

        if not name or not url:
            return jsonify({'error': 'Name and URL are required'}), 400

        # Clean URL to root domain
        clean_url = clean_url_to_root_domain(url)

        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if company exists
        cursor.execute('SELECT id FROM companies WHERE id = ?', (company_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({'error': 'Company not found'}), 404

        # Check if URL conflicts with another company
        cursor.execute('SELECT id FROM companies WHERE url = ? AND id != ?', (clean_url, company_id))
        if cursor.fetchone():
            conn.close()
            return jsonify({'error': 'Another company with this URL already exists'}), 400

        # Update company
        cursor.execute(
            'UPDATE companies SET name = ?, url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            (name, clean_url, company_id)
        )
        conn.commit()
        conn.close()

        logger.info(f"Updated company {company_id}: {name} - {clean_url}")
        return jsonify({
            'success': True,
            'message': 'Company updated successfully',
            'cleaned_url': clean_url
        })

    except Exception as e:
        logger.error(f"Error updating company: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/companies/<int:company_id>', methods=['DELETE'])
def api_delete_company(company_id):
    """Delete a company and all associated data."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if company exists
        cursor.execute('SELECT name FROM companies WHERE id = ?', (company_id,))
        company = cursor.fetchone()
        if not company:
            conn.close()
            return jsonify({'error': 'Company not found'}), 404

        # Get job IDs for this company to delete associated emails
        cursor.execute('SELECT job_id FROM scrape_jobs WHERE company_id = ?', (company_id,))
        job_ids = [row['job_id'] for row in cursor.fetchall()]

        # Delete emails associated with jobs
        for job_id in job_ids:
            cursor.execute('DELETE FROM emails WHERE job_id = ?', (job_id,))

        # Delete scrape jobs
        cursor.execute('DELETE FROM scrape_jobs WHERE company_id = ?', (company_id,))

        # Delete company
        cursor.execute('DELETE FROM companies WHERE id = ?', (company_id,))

        conn.commit()
        conn.close()

        logger.info(f"Deleted company {company_id}: {company['name']}")
        return jsonify({
            'success': True,
            'message': 'Company and all associated data deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting company: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emails', methods=['GET'])
def api_get_emails():
    """Get all emails with company information."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get emails with company information
        cursor.execute('''
            SELECT
                e.id, e.email_address, e.found_on_page, e.created_at,
                c.name as company_name, c.id as company_id,
                sj.job_id
            FROM emails e
            LEFT JOIN scrape_jobs sj ON e.job_id = sj.job_id
            LEFT JOIN companies c ON sj.company_id = c.id
            ORDER BY e.created_at DESC
        ''')
        emails = [dict(row) for row in cursor.fetchall()]

        conn.close()
        return jsonify(emails)

    except Exception as e:
        logger.error(f"Error getting emails: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emails', methods=['POST'])
def api_add_email():
    """Add a new email manually."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        email_address = data.get('email_address', '').strip()
        company_id = data.get('company_id')
        found_on_page = data.get('found_on_page', '').strip()

        if not email_address:
            return jsonify({'error': 'Email address is required'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # Create a manual job entry if company is specified
        job_id = None
        if company_id:
            # Check if company exists
            cursor.execute('SELECT id FROM companies WHERE id = ?', (company_id,))
            if not cursor.fetchone():
                conn.close()
                return jsonify({'error': 'Company not found'}), 404

            # Create a manual job entry
            job_id = f"manual-{str(uuid.uuid4())}"
            cursor.execute('''
                INSERT INTO scrape_jobs (job_id, company_id, url, status, emails_found)
                SELECT ?, ?, url, 'completed', 1 FROM companies WHERE id = ?
            ''', (job_id, company_id, company_id))

        # Insert email
        cursor.execute(
            'INSERT INTO emails (job_id, email_address, found_on_page) VALUES (?, ?, ?)',
            (job_id, email_address, found_on_page or None)
        )
        email_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logger.info(f"Added manual email: {email_address}")
        return jsonify({
            'success': True,
            'email_id': email_id,
            'message': 'Email added successfully'
        })

    except Exception as e:
        logger.error(f"Error adding email: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emails/<int:email_id>', methods=['PUT'])
def api_update_email(email_id):
    """Update an email."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        email_address = data.get('email_address', '').strip()
        found_on_page = data.get('found_on_page', '').strip()

        if not email_address:
            return jsonify({'error': 'Email address is required'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if email exists
        cursor.execute('SELECT id FROM emails WHERE id = ?', (email_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({'error': 'Email not found'}), 404

        # Update email
        cursor.execute(
            'UPDATE emails SET email_address = ?, found_on_page = ? WHERE id = ?',
            (email_address, found_on_page or None, email_id)
        )
        conn.commit()
        conn.close()

        logger.info(f"Updated email {email_id}: {email_address}")
        return jsonify({
            'success': True,
            'message': 'Email updated successfully'
        })

    except Exception as e:
        logger.error(f"Error updating email: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emails/<int:email_id>', methods=['DELETE'])
def api_delete_email(email_id):
    """Delete an email."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if email exists
        cursor.execute('SELECT email_address FROM emails WHERE id = ?', (email_id,))
        email = cursor.fetchone()
        if not email:
            conn.close()
            return jsonify({'error': 'Email not found'}), 404

        # Delete email
        cursor.execute('DELETE FROM emails WHERE id = ?', (email_id,))
        conn.commit()
        conn.close()

        logger.info(f"Deleted email {email_id}: {email['email_address']}")
        return jsonify({
            'success': True,
            'message': 'Email deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting email: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/emails/bulk-delete', methods=['POST'])
def api_bulk_delete_emails():
    """Delete multiple emails."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        email_ids = data.get('email_ids', [])
        if not email_ids:
            return jsonify({'error': 'No email IDs provided'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # Delete emails
        placeholders = ','.join(['?' for _ in email_ids])
        cursor.execute(f'DELETE FROM emails WHERE id IN ({placeholders})', email_ids)
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()

        logger.info(f"Bulk deleted {deleted_count} emails")
        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'Deleted {deleted_count} emails successfully'
        })

    except Exception as e:
        logger.error(f"Error bulk deleting emails: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/companies/<int:company_id>/emails')
def api_get_company_emails(company_id):
    """Get all emails for a specific company."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get company info
        cursor.execute('SELECT name, url FROM companies WHERE id = ?', (company_id,))
        company = cursor.fetchone()
        if not company:
            conn.close()
            return jsonify({'error': 'Company not found'}), 404

        # Get emails for this company
        cursor.execute('''
            SELECT e.id, e.email_address, e.found_on_page, e.created_at, sj.job_id
            FROM emails e
            JOIN scrape_jobs sj ON e.job_id = sj.job_id
            WHERE sj.company_id = ?
            ORDER BY e.email_address
        ''', (company_id,))
        emails = [dict(row) for row in cursor.fetchall()]

        conn.close()
        return jsonify({
            'company': dict(company),
            'emails': emails
        })

    except Exception as e:
        logger.error(f"Error getting company emails: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/companies/<int:company_id>/emails/export')
def api_export_company_emails(company_id):
    """Export emails for a company as CSV."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get company info
        cursor.execute('SELECT name FROM companies WHERE id = ?', (company_id,))
        company = cursor.fetchone()
        if not company:
            conn.close()
            return jsonify({'error': 'Company not found'}), 404

        # Get emails for this company
        cursor.execute('''
            SELECT e.email_address, e.found_on_page, e.created_at
            FROM emails e
            JOIN scrape_jobs sj ON e.job_id = sj.job_id
            WHERE sj.company_id = ?
            ORDER BY e.email_address
        ''', (company_id,))
        emails = cursor.fetchall()

        conn.close()

        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerow(['Email', 'Found On Page', 'Date Found'])

        for email in emails:
            writer.writerow([email['email_address'], email['found_on_page'] or '', email['created_at']])

        csv_content = output.getvalue()
        output.close()

        # Return CSV as download
        return Response(
            csv_content,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename=emails_{company["name"].replace(" ", "_")}_{company_id}.csv'}
        )

    except Exception as e:
        logger.error(f"Error exporting company emails: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "healthy", "service": "email-scraper"})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
